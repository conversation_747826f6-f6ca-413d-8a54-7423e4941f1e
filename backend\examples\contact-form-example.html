<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Example Integration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        input, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .char-count {
            text-align: right;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .char-count.warning {
            color: #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Contact Us</h1>
            <p>We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
        </div>

        <div id="messageContainer"></div>

        <form id="contactForm">
            <div class="form-group">
                <label for="name">Full Name *</label>
                <input type="text" id="name" name="name" required maxlength="100">
            </div>

            <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="subject">Subject *</label>
                <input type="text" id="subject" name="subject" required maxlength="200">
                <div class="char-count" id="subjectCount">0/200</div>
            </div>

            <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" required maxlength="2000" 
                          placeholder="Please describe your inquiry in detail..."></textarea>
                <div class="char-count" id="messageCount">0/2000</div>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Sending your message...</p>
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                Send Message
            </button>
        </form>
    </div>

    <script>
        // API Configuration
        const API_BASE = 'http://localhost:5000'; // Change this to your server URL

        // Form elements
        const form = document.getElementById('contactForm');
        const submitBtn = document.getElementById('submitBtn');
        const loading = document.getElementById('loading');
        const messageContainer = document.getElementById('messageContainer');
        const subjectInput = document.getElementById('subject');
        const messageInput = document.getElementById('message');
        const subjectCount = document.getElementById('subjectCount');
        const messageCount = document.getElementById('messageCount');

        // Character counting
        function updateCharCount(input, counter, maxLength) {
            const currentLength = input.value.length;
            counter.textContent = `${currentLength}/${maxLength}`;
            
            if (currentLength > maxLength * 0.9) {
                counter.classList.add('warning');
            } else {
                counter.classList.remove('warning');
            }
        }

        subjectInput.addEventListener('input', () => {
            updateCharCount(subjectInput, subjectCount, 200);
        });

        messageInput.addEventListener('input', () => {
            updateCharCount(messageInput, messageCount, 2000);
        });

        // Show message function
        function showMessage(message, type = 'success') {
            messageContainer.innerHTML = `
                <div class="message ${type}">
                    ${message}
                </div>
            `;
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    messageContainer.innerHTML = '';
                }, 5000);
            }
        }

        // Show error messages
        function showErrors(errors) {
            const errorList = Array.isArray(errors) 
                ? errors.map(error => `• ${error}`).join('<br>')
                : errors;
            
            showMessage(errorList, 'error');
        }

        // Form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Clear previous messages
            messageContainer.innerHTML = '';
            
            // Get form data
            const formData = new FormData(form);
            const data = {
                name: formData.get('name').trim(),
                email: formData.get('email').trim(),
                subject: formData.get('subject').trim(),
                message: formData.get('message').trim()
            };

            // Basic client-side validation
            if (!data.name || !data.email || !data.subject || !data.message) {
                showErrors('Please fill in all required fields.');
                return;
            }

            if (data.name.length < 2) {
                showErrors('Name must be at least 2 characters long.');
                return;
            }

            if (data.subject.length < 5) {
                showErrors('Subject must be at least 5 characters long.');
                return;
            }

            if (data.message.length < 10) {
                showErrors('Message must be at least 10 characters long.');
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            loading.style.display = 'block';
            
            try {
                const response = await fetch(`${API_BASE}/api/contact`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (response.ok) {
                    // Success
                    showMessage(result.message, 'success');
                    form.reset();
                    updateCharCount(subjectInput, subjectCount, 200);
                    updateCharCount(messageInput, messageCount, 2000);
                } else {
                    // Error
                    if (result.errors && Array.isArray(result.errors)) {
                        showErrors(result.errors);
                    } else {
                        showErrors(result.message || 'An error occurred. Please try again.');
                    }
                }
            } catch (error) {
                console.error('Network error:', error);
                showErrors('Network error. Please check your connection and try again.');
            } finally {
                // Hide loading state
                submitBtn.disabled = false;
                loading.style.display = 'none';
            }
        });

        // Initialize character counts
        updateCharCount(subjectInput, subjectCount, 200);
        updateCharCount(messageInput, messageCount, 2000);
    </script>
</body>
</html>
