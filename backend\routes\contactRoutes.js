import express from 'express';
import {
  submitContactForm,
  getContactSubmissions,
  getContactById,
  updateContactStatus
} from '../controllers/contactController.js';
import { protect, admin } from '../middleware/authMiddleware.js';

const router = express.Router();

// Public route - Submit contact form
router.post('/', submitContactForm);

// Admin routes - Manage contact submissions
router.get('/', protect, admin, getContactSubmissions);
router.get('/:id', protect, admin, getContactById);
router.put('/:id/status', protect, admin, updateContactStatus);

export default router;
