import React, { useState, useEffect, useMemo } from 'react';
import axios from 'axios';
import { Box } from 'lucide-react';

// Import Refactored Components
import Loader from './components/layout/Loader';
import ErrorMessage from './components/layout/ErrorMessage';
import Header from './components/layout/Header';
import SearchBar from './components/ui/SearchBar';
import ViewToggle from './components/ui/ViewToggle';
import CategoryCard from './components/cards/CategoryCard';

const CategoriesPage = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [viewMode, setViewMode] = useState('grid');
  const [searchTerm, setSearchTerm] = useState('');

  const API_BASE_URL = 'http://localhost:5000/api';

  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      try {
        const { data } = await axios.get(`${API_BASE_URL}/categories`);
        setCategories(data);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    fetchCategories();
  }, []);
  
  const filteredCategories = useMemo(() => {
    if (!searchTerm) return categories;
    return categories.filter(cat =>
      cat.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [categories, searchTerm]);

  if (loading) return <Loader />;
  if (error) return <ErrorMessage message={error} />;

  return (
    <div className="min-h-screen bg-slate-50 text-slate-800">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-24">
        <div className="sticky top-4 z-30 mb-8">
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-center bg-white/60 backdrop-blur-lg border border-slate-200 rounded-2xl p-4 shadow-sm">
            <SearchBar searchTerm={searchTerm} onSearchTermChange={(e) => setSearchTerm(e.target.value)} />
            <ViewToggle viewMode={viewMode} onViewModeChange={setViewMode} />
          </div>
        </div>

        <div>
          {filteredCategories.length > 0 ? (
            <div
              className={
                viewMode === 'grid'
                  ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                  : "space-y-4"
              }
            >
              {filteredCategories.map((category) => (
                <CategoryCard
                  key={category._id}
                  category={category}
                  isList={viewMode === 'list'}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <Box className="w-16 h-16 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-700 mb-2">No Categories Found</h3>
              <p className="text-slate-500">Your search for "{searchTerm}" did not match any categories.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoriesPage;