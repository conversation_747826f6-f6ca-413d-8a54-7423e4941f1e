# Contact Us API Documentation

## Overview
The Contact Us API provides endpoints for handling contact form submissions in the e-commerce application. It includes features like form validation, rate limiting, email notifications, and admin management.

## Features
- ✅ Form validation with detailed error messages
- ✅ Rate limiting to prevent spam (3 submissions per 15 minutes per IP)
- ✅ Input sanitization to prevent XSS attacks
- ✅ Automatic email notifications to admin and user
- ✅ Contact submission storage in MongoDB
- ✅ Admin endpoints for managing submissions
- ✅ RESTful API design

## Environment Variables Required
Make sure these are set in your `.env` file:

```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_SERVICE=gmail
TO_EMAIL=<EMAIL>
EMAIL_FROM="Your Site Name"
MONGO_URI=mongodb://localhost:27017/e-commerce
JWT_SECRET=your-jwt-secret
```

## API Endpoints

### 1. Submit Contact Form (Public)
**POST** `/api/contact`

Submit a new contact form.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "subject": "Product Inquiry",
  "message": "I have a question about your products..."
}
```

**Validation Rules:**
- `name`: Required, 2-100 characters
- `email`: Required, valid email format
- `subject`: Required, 5-200 characters
- `message`: Required, 10-2000 characters

**Success Response (201):**
```json
{
  "success": true,
  "message": "Your message has been sent successfully. We will get back to you soon!",
  "data": {
    "id": "contact_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "subject": "Product Inquiry",
    "submittedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    "Name must be at least 2 characters long",
    "Please provide a valid email address"
  ]
}
```

**Rate Limit Response (429):**
```json
{
  "success": false,
  "message": "Too many contact form submissions. Please try again in 900 seconds."
}
```

### 2. Get All Contact Submissions (Admin Only)
**GET** `/api/contact`

Retrieve all contact submissions with pagination.

**Headers:**
```
Authorization: Bearer <admin_jwt_token>
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Filter by status (new, read, replied, resolved)

**Success Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "_id": "contact_id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "subject": "Product Inquiry",
      "message": "I have a question...",
      "status": "new",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### 3. Get Single Contact Submission (Admin Only)
**GET** `/api/contact/:id`

Retrieve a specific contact submission. Automatically marks as "read" if status is "new".

**Headers:**
```
Authorization: Bearer <admin_jwt_token>
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "_id": "contact_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "subject": "Product Inquiry",
    "message": "I have a question...",
    "status": "read",
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0...",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### 4. Update Contact Status (Admin Only)
**PUT** `/api/contact/:id/status`

Update the status of a contact submission.

**Headers:**
```
Authorization: Bearer <admin_jwt_token>
```

**Request Body:**
```json
{
  "status": "replied"
}
```

**Valid Status Values:**
- `new`: Newly submitted
- `read`: Admin has viewed the submission
- `replied`: Admin has responded to the user
- `resolved`: Issue has been resolved

**Success Response (200):**
```json
{
  "success": true,
  "message": "Contact status updated successfully",
  "data": {
    "_id": "contact_id",
    "status": "replied",
    // ... other fields
  }
}
```

## Email Notifications

### Admin Notification
When a contact form is submitted, an email is automatically sent to the admin email (`TO_EMAIL`) with:
- Contact details (name, email, subject)
- Full message content
- Submission metadata (timestamp, IP address, user agent)

### User Confirmation
A confirmation email is sent to the user with:
- Thank you message
- Summary of their submission
- Expected response time information

## Rate Limiting
- **Limit**: 3 submissions per IP address per 15-minute window
- **Purpose**: Prevent spam and abuse
- **Storage**: In-memory (resets on server restart)
- **Response**: HTTP 429 with retry time

## Security Features

### Input Sanitization
All user inputs are sanitized to prevent XSS attacks:
- Removes HTML tags (`<>`)
- Removes JavaScript protocols
- Removes event handlers

### Validation
Comprehensive validation on both client and server side:
- Required field validation
- Length constraints
- Email format validation
- Type checking

## Database Schema

### Contact Model
```javascript
{
  name: String (required, max: 100),
  email: String (required, valid email),
  subject: String (required, max: 200),
  message: String (required, max: 2000),
  status: String (enum: ['new', 'read', 'replied', 'resolved']),
  ipAddress: String,
  userAgent: String,
  createdAt: Date,
  updatedAt: Date
}
```

### Indexes
- `email`: For efficient email lookups
- `status`: For filtering by status
- `createdAt`: For chronological sorting

## Testing

### Manual Testing
1. Start the server: `npm run dev`
2. Use a tool like Postman or curl to test endpoints
3. Check email delivery (make sure SMTP settings are correct)

### Automated Testing
A test script is provided: `test-contact-api.js`

```bash
# Install node-fetch for testing (if not already installed)
npm install node-fetch

# Run tests
node test-contact-api.js
```

## Integration with Frontend

### Example Frontend Usage
```javascript
// Submit contact form
const submitContactForm = async (formData) => {
  try {
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();
    
    if (response.ok) {
      // Show success message
      alert(result.message);
    } else {
      // Show error messages
      console.error(result.errors || result.message);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};
```

## Troubleshooting

### Common Issues

1. **Email not sending**
   - Check EMAIL_USER, EMAIL_PASS, EMAIL_SERVICE in .env
   - For Gmail, use App Password instead of regular password
   - Verify TO_EMAIL is set correctly

2. **Rate limiting too strict**
   - Modify the rate limit parameters in `validation.js`
   - Consider using Redis for production rate limiting

3. **Validation errors**
   - Check field length requirements
   - Ensure email format is valid
   - Verify all required fields are provided

4. **Admin endpoints not working**
   - Ensure user has `isAdmin: true` in database
   - Check JWT token is valid and included in Authorization header

## Production Considerations

1. **Rate Limiting**: Use Redis instead of in-memory storage
2. **Email Queue**: Implement email queue for better reliability
3. **Monitoring**: Add logging and monitoring for contact submissions
4. **Backup**: Regular database backups for contact data
5. **GDPR**: Consider data retention policies for contact submissions
