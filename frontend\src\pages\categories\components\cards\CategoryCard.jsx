import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Tag } from 'lucide-react';

const CategoryCard = ({ category, isList }) => (
  <Link
    to={`/products?category=${category._id}`}
    className={`block bg-white border border-slate-200 rounded-2xl shadow-md overflow-hidden transition-all duration-200 hover:shadow-lg hover:-translate-y-1 ${isList ? 'flex items-center' : ''}`}
  >
    <div className={`relative overflow-hidden ${isList ? 'w-32 h-full flex-shrink-0' : 'h-40 w-full'}`}>
      {category.imageUrl ? (
        <img src={category.imageUrl} alt={category.name} className="w-full h-full object-cover" />
      ) : (
        <div className="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
          <Tag size={32} className="text-slate-400" />
        </div>
      )}
    </div>
    <div className="p-4 flex-grow flex flex-col justify-between">
      <div>
        <h3 className="text-lg font-bold text-slate-800">{category.name}</h3>
        <p className="text-slate-500 text-sm mt-1 mb-4 line-clamp-2">{category.description || "Explore this collection."}</p>
      </div>
      <div className="flex items-center justify-between text-xs font-medium">
        <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
          {category.productCount || 0} products
        </span>
        <div className="text-blue-600 flex items-center gap-1">
          View <ArrowRight size={14} />
        </div>
      </div>
    </div>
  </Link>
);

export default CategoryCard;