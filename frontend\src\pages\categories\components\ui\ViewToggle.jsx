import React from 'react';
import { LayoutGrid, LayoutList } from 'lucide-react';

const ViewToggle = ({ viewMode, onViewModeChange }) => (
  <div className="bg-slate-200/70 rounded-lg p-1 inline-flex">
    <button
      className={`px-4 py-1.5 rounded-md text-sm font-medium flex items-center gap-2 transition-colors ${
        viewMode === 'grid' ? 'bg-blue-600 text-white' : 'text-slate-600 hover:text-slate-900'
      }`}
      onClick={() => onViewModeChange('grid')}
    >
      <LayoutGrid size={16} /> Grid
    </button>
    <button
      className={`px-4 py-1.5 rounded-md text-sm font-medium flex items-center gap-2 transition-colors ${
        viewMode === 'list' ? 'bg-blue-600 text-white' : 'text-slate-600 hover:text-slate-900'
      }`}
      onClick={() => onViewModeChange('list')}
    >
      <LayoutList size={16} /> List
    </button>
  </div>
);

export default ViewToggle;