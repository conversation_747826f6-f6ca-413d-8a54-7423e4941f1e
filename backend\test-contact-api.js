// Simple test script for Contact API
// Run this with: node test-contact-api.js

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:5000';

// Test data
const testContactData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  subject: 'Test Contact Form Submission',
  message: 'This is a test message to verify the contact form API is working correctly. The message should be at least 10 characters long to pass validation.'
};

const invalidContactData = {
  name: '<PERSON>', // Too short
  email: 'invalid-email', // Invalid format
  subject: 'Hi', // Too short
  message: 'Short' // Too short
};

async function testContactAPI() {
  console.log('🧪 Testing Contact API...\n');

  // Test 1: Valid contact form submission
  console.log('📝 Test 1: Valid contact form submission');
  try {
    const response = await fetch(`${API_BASE}/api/contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testContactData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Success:', result.message);
      console.log('📧 Contact ID:', result.data.id);
    } else {
      console.log('❌ Failed:', result.message);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '─'.repeat(50) + '\n');

  // Test 2: Invalid contact form submission
  console.log('📝 Test 2: Invalid contact form submission (should fail validation)');
  try {
    const response = await fetch(`${API_BASE}/api/contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidContactData)
    });

    const result = await response.json();
    
    if (!response.ok) {
      console.log('✅ Validation working correctly');
      console.log('📋 Validation errors:', result.errors);
    } else {
      console.log('❌ Validation should have failed but didn\'t');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '─'.repeat(50) + '\n');

  // Test 3: Missing fields
  console.log('📝 Test 3: Missing required fields');
  try {
    const response = await fetch(`${API_BASE}/api/contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name: 'John' }) // Missing other fields
    });

    const result = await response.json();
    
    if (!response.ok) {
      console.log('✅ Required field validation working');
      console.log('📋 Error message:', result.message);
    } else {
      console.log('❌ Should have failed for missing fields');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '─'.repeat(50) + '\n');

  // Test 4: Rate limiting (multiple requests)
  console.log('📝 Test 4: Rate limiting (sending multiple requests quickly)');
  try {
    const promises = [];
    for (let i = 0; i < 5; i++) {
      promises.push(
        fetch(`${API_BASE}/api/contact`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...testContactData,
            subject: `Rate limit test ${i + 1}`
          })
        })
      );
    }

    const responses = await Promise.all(promises);
    const results = await Promise.all(responses.map(r => r.json()));

    let successCount = 0;
    let rateLimitedCount = 0;

    results.forEach((result, index) => {
      if (responses[index].status === 201) {
        successCount++;
      } else if (responses[index].status === 429) {
        rateLimitedCount++;
      }
    });

    console.log(`✅ Successful submissions: ${successCount}`);
    console.log(`🚫 Rate limited submissions: ${rateLimitedCount}`);
    
    if (rateLimitedCount > 0) {
      console.log('✅ Rate limiting is working correctly');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n🎉 Contact API testing completed!');
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${API_BASE}/api/products`);
    if (response.ok || response.status === 404) {
      return true;
    }
  } catch (error) {
    return false;
  }
  return false;
}

// Main execution
async function main() {
  console.log('🔍 Checking if server is running...');
  
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Server is not running. Please start the server first with: npm run dev');
    console.log('📍 Make sure the server is running on http://localhost:5000');
    process.exit(1);
  }
  
  console.log('✅ Server is running. Starting tests...\n');
  await testContactAPI();
}

main().catch(console.error);
