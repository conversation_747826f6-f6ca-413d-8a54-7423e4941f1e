import React from 'react';
import { Search } from 'lucide-react';

const SearchBar = ({ searchTerm, onSearchTermChange }) => (
  <div className="relative w-full sm:w-auto">
    <input
      type="text"
      placeholder="Search categories..."
      className="w-full sm:w-64 bg-white border border-slate-300 rounded-lg py-2 px-4 pl-10 text-slate-900 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
      value={searchTerm}
      onChange={onSearchTermChange}
    />
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={18} />
  </div>
);

export default SearchBar;