<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Management Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .auth-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .dashboard {
            display: none;
        }

        .filters {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .filter-group {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .contacts-grid {
            display: grid;
            gap: 20px;
        }

        .contact-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
        }

        .contact-card.new {
            border-left-color: #28a745;
        }

        .contact-card.read {
            border-left-color: #ffc107;
        }

        .contact-card.replied {
            border-left-color: #17a2b8;
        }

        .contact-card.resolved {
            border-left-color: #6c757d;
        }

        .contact-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .contact-info {
            flex: 1;
        }

        .contact-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .contact-email {
            color: #666;
            font-size: 0.9rem;
        }

        .contact-date {
            color: #999;
            font-size: 0.8rem;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-new {
            background-color: #d4edda;
            color: #155724;
        }

        .status-read {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-replied {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .status-resolved {
            background-color: #d6d8db;
            color: #383d41;
        }

        .contact-subject {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .contact-message {
            color: #666;
            line-height: 1.5;
            margin-bottom: 15px;
            max-height: 100px;
            overflow: hidden;
            position: relative;
        }

        .contact-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s ease;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-info {
            background-color: #17a2b8;
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
        }

        .pagination button.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Contact Management Dashboard</h1>
        <p>Manage and respond to customer inquiries</p>
    </div>

    <div class="container">
        <!-- Authentication Section -->
        <div class="auth-section" id="authSection">
            <h2>Admin Login</h2>
            <p>Please enter your admin credentials to access the contact management dashboard.</p>
            
            <div class="input-group">
                <label for="adminEmail">Email:</label>
                <input type="email" id="adminEmail" placeholder="<EMAIL>">
            </div>
            
            <div class="input-group">
                <label for="adminPassword">Password:</label>
                <input type="password" id="adminPassword" placeholder="Enter your password">
            </div>
            
            <button class="btn btn-primary" onclick="login()">Login</button>
            
            <div id="authMessage"></div>
        </div>

        <!-- Dashboard Section -->
        <div class="dashboard" id="dashboard">
            <!-- Filters -->
            <div class="filters">
                <div class="filter-group">
                    <label>Status:</label>
                    <select id="statusFilter">
                        <option value="">All Statuses</option>
                        <option value="new">New</option>
                        <option value="read">Read</option>
                        <option value="replied">Replied</option>
                        <option value="resolved">Resolved</option>
                    </select>
                    
                    <button class="btn btn-primary" onclick="loadContacts()">Filter</button>
                    <button class="btn btn-secondary" onclick="refreshContacts()">Refresh</button>
                </div>
            </div>

            <!-- Messages -->
            <div id="messageContainer"></div>

            <!-- Loading -->
            <div class="loading" id="loading" style="display: none;">
                Loading contacts...
            </div>

            <!-- Contacts Grid -->
            <div class="contacts-grid" id="contactsGrid">
                <!-- Contacts will be loaded here -->
            </div>

            <!-- Pagination -->
            <div class="pagination" id="pagination">
                <!-- Pagination will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:5000';
        let authToken = localStorage.getItem('adminToken');
        let currentPage = 1;
        const itemsPerPage = 10;

        // Check if already authenticated
        if (authToken) {
            showDashboard();
            loadContacts();
        }

        // Authentication
        async function login() {
            const email = document.getElementById('adminEmail').value;
            const password = document.getElementById('adminPassword').value;
            
            if (!email || !password) {
                showAuthMessage('Please enter both email and password.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const result = await response.json();

                if (response.ok && result.token) {
                    authToken = result.token;
                    localStorage.setItem('adminToken', authToken);
                    showDashboard();
                    loadContacts();
                } else {
                    showAuthMessage(result.message || 'Login failed. Please check your credentials.', 'error');
                }
            } catch (error) {
                showAuthMessage('Network error. Please try again.', 'error');
            }
        }

        function logout() {
            authToken = null;
            localStorage.removeItem('adminToken');
            document.getElementById('authSection').style.display = 'block';
            document.getElementById('dashboard').style.display = 'none';
        }

        function showDashboard() {
            document.getElementById('authSection').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';
        }

        function showAuthMessage(message, type) {
            const container = document.getElementById('authMessage');
            container.innerHTML = `<div class="${type}" style="margin-top: 15px;">${message}</div>`;
        }

        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Load contacts
        async function loadContacts(page = 1) {
            if (!authToken) return;

            const loading = document.getElementById('loading');
            const grid = document.getElementById('contactsGrid');
            
            loading.style.display = 'block';
            grid.innerHTML = '';

            try {
                const status = document.getElementById('statusFilter').value;
                const params = new URLSearchParams({
                    page: page,
                    limit: itemsPerPage
                });
                
                if (status) {
                    params.append('status', status);
                }

                const response = await fetch(`${API_BASE}/api/contact?${params}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.status === 401) {
                    logout();
                    return;
                }

                const result = await response.json();

                if (response.ok) {
                    displayContacts(result.data);
                    displayPagination(result.pagination);
                    currentPage = page;
                } else {
                    showMessage(result.message || 'Failed to load contacts', 'error');
                }
            } catch (error) {
                showMessage('Network error while loading contacts', 'error');
            } finally {
                loading.style.display = 'none';
            }
        }

        function displayContacts(contacts) {
            const grid = document.getElementById('contactsGrid');
            
            if (contacts.length === 0) {
                grid.innerHTML = '<div class="loading">No contacts found.</div>';
                return;
            }

            grid.innerHTML = contacts.map(contact => `
                <div class="contact-card ${contact.status}">
                    <div class="contact-header">
                        <div class="contact-info">
                            <div class="contact-name">${contact.name}</div>
                            <div class="contact-email">${contact.email}</div>
                            <div class="contact-date">${new Date(contact.createdAt).toLocaleString()}</div>
                        </div>
                        <span class="status-badge status-${contact.status}">${contact.status}</span>
                    </div>
                    
                    <div class="contact-subject">${contact.subject}</div>
                    <div class="contact-message">${contact.message.substring(0, 200)}${contact.message.length > 200 ? '...' : ''}</div>
                    
                    <div class="contact-actions">
                        <button class="btn btn-primary" onclick="viewContact('${contact._id}')">View Details</button>
                        ${contact.status === 'new' ? `<button class="btn btn-success" onclick="updateStatus('${contact._id}', 'read')">Mark as Read</button>` : ''}
                        ${contact.status === 'read' ? `<button class="btn btn-info" onclick="updateStatus('${contact._id}', 'replied')">Mark as Replied</button>` : ''}
                        ${contact.status === 'replied' ? `<button class="btn btn-secondary" onclick="updateStatus('${contact._id}', 'resolved')">Mark as Resolved</button>` : ''}
                    </div>
                </div>
            `).join('');
        }

        function displayPagination(pagination) {
            const container = document.getElementById('pagination');
            const { page, pages } = pagination;
            
            let paginationHTML = '';
            
            // Previous button
            paginationHTML += `<button onclick="loadContacts(${page - 1})" ${page <= 1 ? 'disabled' : ''}>Previous</button>`;
            
            // Page numbers
            for (let i = Math.max(1, page - 2); i <= Math.min(pages, page + 2); i++) {
                paginationHTML += `<button onclick="loadContacts(${i})" ${i === page ? 'class="active"' : ''}>${i}</button>`;
            }
            
            // Next button
            paginationHTML += `<button onclick="loadContacts(${page + 1})" ${page >= pages ? 'disabled' : ''}>Next</button>`;
            
            container.innerHTML = paginationHTML;
        }

        // Update contact status
        async function updateStatus(contactId, newStatus) {
            try {
                const response = await fetch(`${API_BASE}/api/contact/${contactId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ status: newStatus })
                });

                const result = await response.json();

                if (response.ok) {
                    showMessage('Status updated successfully', 'success');
                    loadContacts(currentPage);
                } else {
                    showMessage(result.message || 'Failed to update status', 'error');
                }
            } catch (error) {
                showMessage('Network error while updating status', 'error');
            }
        }

        // View contact details
        async function viewContact(contactId) {
            try {
                const response = await fetch(`${API_BASE}/api/contact/${contactId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    const contact = result.data;
                    alert(`
Contact Details:

Name: ${contact.name}
Email: ${contact.email}
Subject: ${contact.subject}
Status: ${contact.status}
Submitted: ${new Date(contact.createdAt).toLocaleString()}

Message:
${contact.message}

IP Address: ${contact.ipAddress || 'N/A'}
                    `);
                    
                    // Refresh the list to show updated status
                    loadContacts(currentPage);
                } else {
                    showMessage(result.message || 'Failed to load contact details', 'error');
                }
            } catch (error) {
                showMessage('Network error while loading contact details', 'error');
            }
        }

        function refreshContacts() {
            loadContacts(currentPage);
        }

        // Event listeners
        document.getElementById('adminPassword').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                login();
            }
        });

        document.getElementById('statusFilter').addEventListener('change', function() {
            loadContacts(1);
        });
    </script>
</body>
</html>
