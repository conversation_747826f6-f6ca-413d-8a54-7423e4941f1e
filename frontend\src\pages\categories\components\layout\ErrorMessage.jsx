import React from 'react';
import { XCircle } from 'lucide-react';

const ErrorMessage = ({ message }) => (
  <div className="min-h-screen bg-slate-50 flex items-center justify-center text-center p-4">
    <div>
      <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
      <h2 className="text-2xl font-bold text-slate-800 mb-2">An Error Occurred</h2>
      <p className="text-slate-500">{message}</p>
    </div>
  </div>
);

export default ErrorMessage;