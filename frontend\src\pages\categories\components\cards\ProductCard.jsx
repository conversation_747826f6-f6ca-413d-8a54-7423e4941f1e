import React from 'react';
import { Link } from 'react-router-dom';
import { ShoppingBag } from 'lucide-react';

const ProductCard = ({ product }) => (
  <div className="group bg-white rounded-2xl border border-slate-200/80 overflow-hidden shadow-sm transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
    <Link to={`/product/${product._id}`}>
      <div className="aspect-square overflow-hidden bg-slate-50">
        <img
          src={product.images[0]}
          alt={product.name}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
        />
      </div>
      <div className="p-4">
        <p className="text-sm text-blue-600 mb-1">{product.category.name || 'Category'}</p>
        <h3 className="font-bold text-slate-800 truncate mb-2">{product.name}</h3>
        <div className="flex items-center justify-between">
          <p className="text-lg font-semibold text-slate-900">${product.price.toFixed(2)}</p>
          <button className="p-2 rounded-full bg-blue-100 text-blue-700 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-blue-200">
            <ShoppingBag size={18} />
          </button>
        </div>
      </div>
    </Link>
  </div>
);

export default ProductCard;