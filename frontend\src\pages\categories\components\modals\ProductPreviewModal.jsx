import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Package } from 'lucide-react';
import ProductCard from '../cards/ProductCard';

const ProductPreviewModal = ({ isOpen, onClose, category, products, loading: productsLoading }) => {
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4" onClick={onClose}>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3 }}
          className="bg-slate-50 rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="sticky top-0 bg-white/80 backdrop-blur-sm border-b border-slate-200 p-5 flex justify-between items-center z-10">
            <div>
              <h2 className="text-2xl font-bold text-slate-900">{category?.name}</h2>
              <p className="text-slate-500 mt-1">{products?.length || 0} products available</p>
            </div>
            <button onClick={onClose} className="p-2 rounded-full hover:bg-slate-200 transition-colors">
              <X size={24} className="text-slate-500" />
            </button>
          </div>

          <div className="p-6 overflow-y-auto">
            {productsLoading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 py-8">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse bg-slate-200 rounded-xl w-full aspect-[4/5]" />
                ))}
              </div>
            ) : products && products.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {products.map((product) => (
                  <ProductCard key={product._id} product={product} />
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <Package className="w-16 h-16 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-700 mb-2">No Products Found</h3>
                <p className="text-slate-500">This category currently has no products.</p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default ProductPreviewModal;