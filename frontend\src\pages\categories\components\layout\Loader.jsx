import React from 'react';
import { motion } from 'framer-motion';

const Loader = () => (
  <div className="min-h-screen w-full flex items-center justify-center">
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
      className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full"
    />
  </div>
);

export default Loader;